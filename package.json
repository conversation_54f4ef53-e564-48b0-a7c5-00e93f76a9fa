{"name": "srpg-front", "version": "2.0.0", "description": "СРПГ", "main": "index.js", "scripts": {"start:center": "cross-env STAND=main NODE_ENV=development server=center MODE=center webpack serve --config config/webpack.dev.js", "start:dc": "cross-env STAND=main NODE_ENV=development server=dc MODE=dc webpack serve --config config/webpack.dev.js", "start-east:center": "cross-env STAND=east NODE_ENV=development server=center MODE=center webpack serve --config config/webpack.dev.js", "start-east:dc": "cross-env STAND=east NODE_ENV=development server=dc MODE=dc webpack serve --config config/webpack.dev.js", "start:dev": "concurrently --kill-others \"npm run start:center\" \"npm run start:dc\" \"npm run start-east:center\" \"npm run start-east:dc\"", "build:center": "cross-env STAND=main NODE_ENV=production MODE=center webpack --config config/webpack.prod.js", "build:dc": "cross-env STAND=main NODE_ENV=production MODE=dc webpack --config config/webpack.prod.js", "build-east:center": "cross-env STAND=east NODE_ENV=production MODE=center webpack --config config/webpack.prod.js", "build-east:dc": "cross-env STAND=east NODE_ENV=production MODE=dc webpack --config config/webpack.prod.js", "build": "concurrently --kill-others \"npm run build:center\" \"npm run build:dc\" \"npm run build-east:center\" \"npm run build-east:dc\""}, "repository": {"type": "git", "url": "git+https://gitlab.com/ups-ic/srpg/front.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://gitlab.com/ups-ic/srpg/front/issues"}, "homepage": "https://gitlab.com/ups-ic/srpg/front#readme", "dependencies": {"@devexpress/dx-react-core": "4.0.2", "@devexpress/dx-react-grid": "4.0.2", "@devexpress/dx-react-grid-export": "4.0.2", "@devexpress/dx-react-grid-material-ui": "4.0.2", "@emotion/react": "11.10.5", "@emotion/styled": "11.10.5", "@mui/icons-material": "5.11.0", "@mui/lab": "5.0.0-alpha.115", "@mui/material": "5.11.4", "@mui/styled-engine": "5.11.0", "@mui/x-date-pickers": "6.16.1", "@mui/x-date-pickers-pro": "6.16.1", "@svgr/webpack": "6.2.0", "@tippyjs/react": "4.2.6", "axios": "0.30.0", "cross-env": "7.0.3", "css-loader": "6.5.1", "date-fns": "2.30.0", "dompurify": "3.2.6", "dotenv": "10.0.0", "dotenv-webpack": "7.0.3", "file-loader": "6.2.0", "file-saver": "2.0.5", "framer-motion": "6.2.1", "lodash": "4.17.21", "mobx": "6.3.12", "mobx-react": "7.2.1", "node-sass": "7.0.1", "polished": "4.1.3", "query-string": "7.1.0", "react": "17.0.2", "react-dom": "17.0.2", "react-dropzone": "12.0.5", "react-router-dom": "6.2.1", "style-loader": "3.3.1", "styled-components": "4.4.1", "styled-components-modifiers": "1.2.5", "tippy.js": "6.3.7", "ts-loader": "9.2.6", "uuid": "8.3.2"}, "devDependencies": {"@babel/core": "7.16.7", "@babel/plugin-proposal-class-properties": "7.16.7", "@babel/plugin-proposal-decorators": "7.16.7", "@babel/plugin-transform-runtime": "7.16.7", "@babel/preset-env": "7.16.7", "@babel/preset-react": "7.16.7", "@babel/preset-typescript": "7.16.7", "@types/file-saver": "2.0.5", "@types/lodash": "4.14.178", "@types/react-dom": "17.0.11", "@types/styled-components": "5.1.19", "@typescript-eslint/eslint-plugin": "5.9.1", "@typescript-eslint/parser": "5.9.1", "babel-loader": "8.2.3", "babel-plugin-styled-components": "2.0.2", "case-sensitive-paths-webpack-plugin": "2.4.0", "clean-webpack-plugin": "4.0.0", "concurrently": "7.0.0", "copy-webpack-plugin": "10.2.0", "css-minimizer-webpack-plugin": "3.3.1", "eslint": "8.6.0", "eslint-config-prettier": "8.3.0", "eslint-config-react-app": "7.0.0", "eslint-plugin-flowtype": "8.0.3", "eslint-plugin-import": "2.25.4", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-react": "7.28.0", "eslint-plugin-react-hooks": "4.3.0", "html-webpack-plugin": "5.5.0", "mini-css-extract-plugin": "2.4.6", "prettier": "2.5.1", "sass": "1.48.0", "terser-webpack-plugin": "5.3.7", "typescript": "4.5.4", "uglify-js": "3.17.4", "url-loader": "4.1.1", "webpack": "5.94.0", "webpack-cli": "4.10.0", "webpack-dev-server": "4.7.4", "webpack-merge": "5.8.0"}}