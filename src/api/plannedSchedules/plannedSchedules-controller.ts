import { axiosInstance as api } from "utils/axios";
import { isModeCenter, isEast } from "utils/getMode";
import axios, { AxiosRequestConfig } from "axios";
const CancelToken = axios.CancelToken;
export const sourceDC = CancelToken.source();
export const controller = new AbortController();
const { signal } = controller;

interface itemsProps {
  pgId: string;
  lastTaskId: string;
  name: string;
  type: string;
  createdAt: string;
  startAt: string;
  endAt: string;
  syncZone: string;
  source: string;
}

interface viewLoadedPGProps {
  items: itemsProps[];
}

export type SummaryStatus = "STAGE1" | "STAGE2" | "DONE" | "ERROR" | "COND_ERROR";

export interface SummaryStatusResponse {
  status: SummaryStatus;
  messages: {
    status: SummaryStatus;
    message: string;
  }[];
  rid: string;
  timestamp: string;
}

export interface AckMessage {
  id: string;
  pgId: string;
  pgName: string;
  text: string;
  kind: string;
  createdAt: string;
}

export interface AckMessagesResponse {
  messages: AckMessage[];
  rid: string;
  timestamp: string;
}

interface ObjectPost {
  taskId: string;
  toLoad: boolean;
}

export interface AlreadyAccepted {
  actionDate: string; // ISO YYYY-MM-DD
  dates: string[]; // список дат вида "ДД.ММ.ГГГГ ЧЧ:ММ:СС"
  pgNum?: number; // номер ПГ (опциональный, так как не все типы графиков имеют номер)
}

export const loadedPG = (date: string, isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, viewLoadedPGProps>(`/srpg-center/api/v1/pg/loaded`, { date });
  } else {
    return api.post<{}, viewLoadedPGProps>(`/srpg-control/api/v1/pg/list`, { date });
  }
};

export const loadType = () => {
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/pg/oik/types`);
};

export const loadPBR = (isModeCenter: any) => {
  if (isModeCenter) {
    return api.get<{}, { items: any[] }>(`/srpg-center/api/v1/pg/parameters/pbr`);
  } else {
    return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/pg/parameters/pbr`);
  }
};

export const loadUDDG = (isModeCenter: any) => {
  if (isModeCenter) {
    return api.get<{}, { items: any[] }>(`/srpg-center/api/v1/pg/parameters/uddg`);
  } else {
    return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/pg/parameters/uddg`);
  }
};

export const loadRecordsEG = () => {
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/pg/types/eg3`);
};

export const saveRecordEG = (items: any[]) => {
  return api.put<{}, {}>(`/srpg-control/api/v1/pg/types/eg3`, { items });
};

export const viewLoadedPG = (date: string) => {
  return api.post<{}, viewLoadedPGProps>(`/srpg-center/api/v1/pg/distribution/loaded`, { date });
};

export const alreadyExist = (objectPost: ObjectPost) => {
  return api.post(`/srpg-center/api/v1/pg/already-exist`, objectPost);
};

export const loadedLater = (objectPost: ObjectPost) => {
  return api.post(`/srpg-center/api/v1/pg/loaded-later`, objectPost);
};

export const notAccepted = (objectPost: ObjectPost) => {
  return api.post(`/srpg-center/api/v1/pg/not-accepted`, objectPost);
};

export const getDataForSelectedPlannedSchedules = (taskId: string) => {
  return api.post<{}, { item: any; canMakeGlobalAccept: any; canRepeatGlobalAccept: any }>(
    `/srpg-center/api/v1/pg/distribution/status`,
    { taskId },
    { cancelToken: sourceDC.token, signal }
  );
};

export const resetDistribution = (taskId: string, controlIds: any[]) => {
  return api.post<{}, { item: any; title: string; message: string; type: string; isKeepOnScreen: boolean }>(`/srpg-center/api/v1/pg/distribution/restart`, {
    taskId,
    controlIds,
  });
};

export const loadPpbr = (date: any) => {
  return api.post(`/srpg-center/api/v1/pg/import/ppbr`, { date });
};

export const loadPbr = (date: any, pgNum: any) => {
  return api.post(`/srpg-center/api/v1/pg/import/pbr`, { date, pgNum });
};

export const loadPdg = (date: any) => {
  if (isEast) {
    return api.post(`/srpg-center/api/v1/pg/import/pdg-2`, { date });
  }
  return api.post(`/srpg-center/api/v1/pg/import/pdg`, { date });
};

export const loadPer = (date: any) => {
  if (isEast) {
    return api.post(`/srpg-center/api/v1/pg/import/per-2`, { date });
  }
  return api.post(`/srpg-center/api/v1/pg/import/per`, { date });
};

export const loadDdg = (date: any) => {
  return api.post(`/srpg-center/api/v1/pg/import/ddg-2`, { date });
};

export const loadUddg = (date: any, pgNum: any) => {
  return api.post(`/srpg-center/api/v1/pg/import/uddg-2`, { date, pgNum });
};

export const getStatus = () => {
  return api.get<{}, { items: any }>(`/srpg-center/api/v1/pg/last-tasks`);
};

export const getSummaryStatus = (pgId: string, config?: AxiosRequestConfig): Promise<SummaryStatusResponse> => {
  return api.post(`/srpg-center/api/v1/pg/summary-status`, { pgId }, config);
};

export const saveUnSaveWrongHours = (taskId: any, toLoad: boolean) => {
  return api.post<{}, { items: any }>(`/srpg-center/api/v1/pg/wrong-hours`, { taskId, toLoad });
};

export const loadDataForSelected = (pgId: string, isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, any>(`/srpg-center/api/v1/pg`, { pgId });
  } else {
    return api.get<{}, any>(`/srpg-control/api/v1/pg/${pgId}`);
  }
};

export const distributePG = (id: any) => {
  return api.post<{}, { title: string; message: string; type: string; isKeepOnScreen: boolean }>(`/srpg-center/api/v1/pg/global-accept`, { id, isAccepted: true });
};

export const getDataXml = (pgId: string) => {
  return api.get<{}, {}>(`/srpg-center/api/v1/pg/${pgId}/xml`, { responseType: "blob" });
};

export const getDataDetailXml = (pgId: string) => {
  return api.get<{}, {}>(`/srpg-center/api/v1/pg/${pgId}/detailed-xml`, { responseType: "blob" });
};

export const acceptSystems = ({ id, isAccepted }: { id: string; isAccepted: string }) => {
  return api.post<{}, {}>(`/srpg-control/api/v1/pg/global-accept`, { id, isAccepted });
};

export const retrySystem = (systemStatusId: any) => {
  return api.post<{}, { title: string; message: string; type: string; isKeepOnScreen: boolean }>(`/srpg-control/api/v1/pg/re-export`, { systemStatusId });
};

export const loadFile = (formdata: FormData, isCenter: boolean) => {
  const url = isModeCenter && isCenter ? `/srpg-center/api/v1/pg/import/xml` : `/srpg-control/api/v1/pg/import/xml`;

  // Переопределяем заголовок Content-Type для отправки файла XML.
  // Так как глобальная настройка axios уже установлена на 'application/json',
  // мы переопределяем его только для этого запроса
  const config = {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  };

  return api.post<{}, unknown>(url, formdata, config);
};

export const retryAccept = (pgId: any) => {
  return api.post<{}, {}>(`/srpg-control/api/v1/pg/repeat-accept`, { pgId });
};

export const sendPreOik = (pgId: any) => {
  return api.post<{}, { title: string; message: string; type: string; isKeepOnScreen: boolean }>(`/srpg-control/api/v1/pg/store-pre-oik`, { pgId });
};

export const repeatSendAccept = (taskId: any, controlIds: any) => {
  return api.post<{}, { title: string; message: string; type: string; isKeepOnScreen: boolean }>(`/srpg-center/api/v1/pg/distribution/repeat/global-accept`, {
    taskId,
    controlIds,
  });
};

export const sendPGBeforeAccept = (pgId: any) => {
  return api.post<{}, { title: string; message: string; type: string; isKeepOnScreen: boolean }>(`/srpg-control/api/v1/pg/repeat-local-accept`, { pgId });
};

export const resetAccept = (pgId: any, systemType: string) => {
  return api.post<{}, { title: string; message: string; type: string; isKeepOnScreen: boolean }>(`/srpg-control/api/v1/pg/re-accept`, { pgId, systemType });
};

export const tryAccept = (id: string) => {
  return api.post<{}, { showModal: boolean; alreadyAccepted: AlreadyAccepted }>(`/srpg-center/api/v1/pg/check-global-accept`, {
    id,
  });
};

export const getEventsDepartments = (controlId: any) => {
  return api.get<{}, { events: any[] }>(`/srpg-center/api/v1/pg/distribution/${controlId}/events`);
};

export const getAckMessages = (): Promise<AckMessagesResponse> => {
  return api.get(`/srpg-center/api/v1/pg/ack`);
};

export const deleteAckMessage = (id: string) => {
  return api.delete(`/srpg-center/api/v1/pg/ack/${id}`);
};

export const getExternalSystemEvents = (system: any, pgId: any) => {
  return api.get<{}, { systemName: any; pgName: any; events: any }>(`/srpg-control/api/v1/pg/${pgId}/system/${system}/events`);
};
