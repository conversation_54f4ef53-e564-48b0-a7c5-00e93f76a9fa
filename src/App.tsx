import React, { useEffect, useState } from "react";
import { ThemeProvider } from "styled-components";
import { GlobalStyle, SRPG_DEFAULT, SRPG_DARK, SRDK_LIGHT, SRDK_DARK } from "./utils";

import { Routes } from "routes/Routes";
import { observer, Provider } from "mobx-react";
import { stores } from "stores/RootStore";
import { Toast } from "components/NotificationContainer/Toast";
import { idb } from "utils/indexDB";
import { useStorageSync } from "./hooks/useStorageSync";

// Удалены выводы в консоль в связи с обращением СИБов (SRPG-2840)
const createCollectionsIndexedDB = () => {
  if (!idb) {
    return;
  }
  const request = idb.open("table-db", 1);

  request.onerror = () => {};
  request.onupgradeneeded = (event) => {
    const db = request.result;
    if (!db.objectStoreNames.contains("userData")) {
      db.createObjectStore("userData", {
        keyPath: "id",
      });
    }
  };

  request.onsuccess = () => {};
};

export const App = observer(() => {
  const localStorageTheme = localStorage.getItem("theme");

  useStorageSync();

  useEffect(() => {
    createCollectionsIndexedDB();
  }, []);

  // @ts-ignore
  const defaultTheme = window?.THEME_DEFAULT ? window?.THEME_DEFAULT : "SRPG_DEFAULT";
  // @ts-ignore
  const themeEnable = window?.THEME_EDIT_ENABLE ? window?.THEME_EDIT_ENABLE : true;
  const themes: any = {
    SRPG_DEFAULT: SRPG_DEFAULT,
    SRPG_DARK: SRPG_DARK,
    SRDK_LIGHT: SRDK_LIGHT,
    SRDK_DARK: SRDK_DARK,
  };
  const isFindTheme = Object.keys(themes).some((el) => el === localStorageTheme);

  const getInitSelectedTheme = () => {
    if (!themeEnable) {
      return defaultTheme;
    }
    if (isFindTheme) {
      return localStorageTheme;
    } else {
      return defaultTheme;
    }
  };

  const initSelectedTheme = getInitSelectedTheme();
  const [selectedTheme, setSelectedTheme] = useState<any>(initSelectedTheme);

  if (process.env.NODE_ENV === "production") {
    console.log = () => {};
    console.error = () => {};
    console.debug = () => {};
  }

  return (
    <>
      {stores && (
        <Provider store={stores}>
          <ThemeProvider theme={themes[selectedTheme]}>
            <GlobalStyle />
            <Routes selectedTheme={selectedTheme} setSelectedTheme={setSelectedTheme} themeEnable={themeEnable} />
            <Toast />
          </ThemeProvider>
        </Provider>
      )}
    </>
  );
});
