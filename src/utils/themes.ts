import { blue, neutral, red, green, yellow } from "./colors";
import { primaryFont } from "./typography";

export const SRPG_DEFAULT = {
  primaryColor: "#0071FF",
  primaryColorHover: "#0263d9",
  primaryColorActive: "#0248a7",
  lightGray: "#e0e2e7",
  gray: "#919191",
  white: "#ffffff",
  black: "#232325",
  lightBlack: "#11263c",
  backgroundColor: "#ffffff", //edf5ff
  hover: "#edf5ff",
  backgroundColorSecondary: "#ffffff",
  textColor: "#232325",
  redBackground: "#FFDBDB",
  red: "#F32D2D",
  redHover: "#bb2323",
  redActive: "#841818",
  folderColor: "#e5ae30",
  greenActiveSupport: "#34B53A",
  greenLightSupport: "#E2FBD7",
  redActiveSupport: "#FF3A29",
  redLightSupport: "#FFE5D3",
  blueActiveSupport: "#0071FF",
  blueLightSupport: "#edf5ff",
  orangeActiveSupport: "#FFB200",
  orangeLightSupport: "#FFF5CC",
  orangeVeryLightSupport: "#FFF8E4FF",
  purpleActiveSupport: "#4339F2",
  purpleLightSupport: "#DAD7FE",
  backgroundMenuColor: "#ffffff",
  topMenuTextColor: "#232325",
  topMenuActiveColor: "#0071FF",
  buttonGroupsTextColor: "#232325",
  buttonGroupsTextColorActive: "#0071FF",
  buttonGroupsTextColorHover: "#ffffff",
  buttonGroupsBackgroundColor: "#ffffff",
  buttonGroupsBackgroundColorActive: "#0071FF",
  buttonGroupsBackgroundColorHover: "#0071FF",
  colorExcel: "#207245",
  colorGuide: "#0071FF",
  buttonSecondaryColor: "#0071FF",
  buttonSecondaryColorActive: "#0360d7",
  inputDisabledText: "#808080",
  inputDisabledBackground: "#f6f6f6",
  rowColorOrange: "#FFF8E4FF",
  rowColorOrangeHover: "#fbf4aa",
  rowSelected: "#f4f4f4",
  borderTh: "#e0e2e7",
  rowColorBlue: "#0071ff47",
  rowColorBlueHover: "#0071FFD8",
  rowColorCdu: "#d0dbeb",
  rowColorCduHover: "#bac7dc",
  rowColorOdu: "#daeadb",
  rowColorOduHover: "#c5ddc7",
  rowColorRdu: "#f7edd4",
  rowColorRduHover: "#f0e2b8",
  rowColorError: "#fff0f0",
  rowColorErrorHover: "#ffe0e0",
};

export const SRPG_DARK = {
  primaryColor: "#0071FF",
  primaryColorHover: "#0263d9",
  primaryColorActive: "#0248a7",
  lightGray: "#4c4c4c",
  gray: "#4c4c4c",
  white: "#ffffff",
  black: "#ffffff",
  lightBlack: "#11263c",
  backgroundColor: "#2c2c2e", //1c1c1e
  backgroundColorSecondary: "#2c2c2e",
  textColor: "#cdcdcd",
  folderColor: "#e5ae30",
  greenActiveSupport: "#34B53A",
  greenLightSupport: "#E2FBD7",
  redActiveSupport: "#FF3A29",
  redLightSupport: "#FFE5D3",
  blueActiveSupport: "#0071FF",
  blueLightSupport: "#2c2c2e", //edf5ff
  hover: "#EDF5FF49",
  orangeActiveSupport: "#FFB200",
  orangeLightSupport: "#FFF5CC",
  orangeVeryLightSupport: "#FFF8E4FF",
  purpleActiveSupport: "#4339F2",
  purpleLightSupport: "#DAD7FE",
  backgroundMenuColor: "#2c2c2e",
  topMenuTextColor: "#cdcdcd",
  topMenuActiveColor: "#0071FF",
  buttonGroupsTextColor: "#ffffff",
  buttonGroupsTextColorActive: "#cdcdcd",
  buttonGroupsTextColorHover: "#ffffff",
  buttonGroupsBackgroundColor: "#2c2c2e",
  buttonGroupsBackgroundColorActive: "#0071FF",
  buttonGroupsBackgroundColorHover: "#0071FF",
  colorExcel: "#207245",
  colorGuide: "#0071FF",
  buttonSecondaryColor: "#FFFFFF",
  buttonSecondaryColorActive: "#c2c2c2",
  inputDisabledText: "#cdcdcd",
  inputDisabledBackground: "#1c1c1e",
  rowColorOrange: "#ffffff3b",
  rowColorOrangeHover: "#FFFFFF5E",
  rowSelected: "#96919157",
  borderTh: "#e0e0e0",
  rowColorBlue: "#0071ff47",
  rowColorBlueHover: "#0071FFD8",
  // CDU (синие оттенки — "система")
  rowColorCdu: "#2E3B4E", // тёмно-синий-заливка
  rowColorCduHover: "#3B4A61",

  // ODU (зелёные оттенки — "оперативка")
  rowColorOdu: "#2F4A38", // тёмно-зелёный
  rowColorOduHover: "#3C5E47",

  // RDU (песочно-жёлтые оттенки — регионы)
  rowColorRdu: "#4A3E2A", // тёплый тёмно-коричнево-жёлтый
  rowColorRduHover: "#615237",

  // Ошибки
  rowColorError: "rgba(255, 70, 70, 0.15)", // полупрозрачный красный
  rowColorErrorHover: "rgba(255, 70, 70, 0.25)",
};

export const SRDK_LIGHT = {
  primaryColor: "#094FA4",
  primaryColorHover: "#063574",
  primaryColorActive: "#052a5a",
  lightGray: "#b8b8b8",
  gray: "#919191",
  white: "#ffffff",
  black: "#232325",
  lightBlack: "#11263c",
  backgroundColor: "#ffffff", //E5E5E5
  hover: "#E5E5E5",
  backgroundColorSecondary: "#ffffff",
  textColor: "#232325",
  redBackground: "#FFDBDB",
  red: "#F32D2D",
  redHover: "#bb2323",
  redActive: "#841818",
  folderColor: "#e5ae30",
  greenActiveSupport: "#339944",
  greenLightSupport: "#E2FBD7",
  redActiveSupport: "#B81414",
  redLightSupport: "#FFE5D3",
  blueActiveSupport: "#094FA4",
  blueLightSupport: "#edf5ff",
  orangeActiveSupport: "#e0a51a",
  orangeLightSupport: "#FFF5CC",
  orangeVeryLightSupport: "#FFF8E4FF",
  purpleActiveSupport: "#3D4C5C",
  purpleLightSupport: "#DAD7FE",
  backgroundMenuColor: "#094FA4",
  topMenuTextColor: "#ffffff",
  topMenuActiveColor: "#ffffff",
  buttonGroupsTextColor: "#ffffff",
  buttonGroupsTextColorActive: "#ffffff",
  buttonGroupsTextColorHover: "#094FA4",
  buttonGroupsBackgroundColor: "#094FA4",
  buttonGroupsBackgroundColorActive: "#ffffff",
  buttonGroupsBackgroundColorHover: "#ffffff",
  colorExcel: "#207245",
  colorGuide: "#E5E5E5",
  buttonSecondaryColor: "#094FA4",
  buttonSecondaryColorActive: "#063371",
  inputDisabledText: "#808080",
  inputDisabledBackground: "#f6f6f6",
  rowColorOrange: "#FFF8E4FF",
  rowColorOrangeHover: "#fbf4aa",
  rowSelected: "#d9d9d9",
  borderTh: "#e0e2e7",
  rowColorBlue: "#0071ff47",
  rowColorBlueHover: "#0071FFD8",
  rowColorCdu: "#d0dbeb",
  rowColorCduHover: "#bac7dc",
  rowColorOdu: "#daeadb",
  rowColorOduHover: "#c5ddc7",
  rowColorRdu: "#f7edd4",
  rowColorRduHover: "#f0e2b8",
  rowColorError: "#fff0f0",
  rowColorErrorHover: "#ffe0e0",
};

export const SRDK_DARK = {
  primaryColor: "#AE8F6F",
  primaryColorHover: "#806951",
  primaryColorActive: "#5a4b3a",
  lightGray: "#4c4c4c",
  gray: "#8b8b8b",
  white: "#ffffff",
  black: "#ffffff",
  lightBlack: "#11263c",
  backgroundColor: "#2a343f", //000000
  hover: "#000000",
  backgroundColorSecondary: "#2a343f",
  textColor: "#cdcdcd",
  folderColor: "#e5ae30",
  greenActiveSupport: "#339944",
  greenLightSupport: "#E2FBD7",
  redActiveSupport: "#B81414",
  redLightSupport: "#FFE5D3",
  blueActiveSupport: "#094FA4",
  blueLightSupport: "#edf5ff",
  orangeActiveSupport: "#AE8F6F",
  orangeLightSupport: "#FFF5CC",
  orangeVeryLightSupport: "#FFF8E4FF",
  purpleActiveSupport: "#3D4C5C",
  purpleLightSupport: "#DAD7FE",
  backgroundMenuColor: "#2a343f",
  topMenuTextColor: "#ffffff",
  topMenuActiveColor: "#ffffff",
  buttonGroupsTextColor: "#cdcdcd",
  buttonGroupsTextColorActive: "#AE8F6F",
  buttonGroupsTextColorHover: "#ffffff",
  buttonGroupsBackgroundColor: "#2a343f",
  buttonGroupsBackgroundColorActive: "#AE8F6F",
  buttonGroupsBackgroundColorHover: "#AE8F6F",
  colorExcel: "#207245",
  colorGuide: "#AE8F6F",
  buttonSecondaryColor: "#FFFFFF",
  buttonSecondaryColorActive: "#b4b4b4",
  inputDisabledText: "#cdcdcd",
  inputDisabledBackground: "#212832",
  rowColorOrange: "#ffffff3b",
  rowColorOrangeHover: "#FFFFFF5E",
  rowSelected: "#303030",
  borderTh: "#e0e0e0",
  rowColorBlue: "#0071ff47",
  rowColorBlueHover: "#0071FFD8",
  // CDU (синие оттенки — "система")
  rowColorCdu: "#2E3B4E", // тёмно-синий-заливка
  rowColorCduHover: "#3B4A61",

  // ODU (зелёные оттенки — "оперативка")
  rowColorOdu: "#2F4A38", // тёмно-зелёный
  rowColorOduHover: "#3C5E47",

  // RDU (песочно-жёлтые оттенки — регионы)
  rowColorRdu: "#4A3E2A", // тёплый тёмно-коричнево-жёлтый
  rowColorRduHover: "#615237",

  // Ошибки
  rowColorError: "rgba(255, 70, 70, 0.15)", // полупрозрачный красный
  rowColorErrorHover: "rgba(255, 70, 70, 0.25)",
};
