import React, { FC } from "react";
import { Modal } from "components/Modal";
import { AlreadyAccepted } from "~/api/plannedSchedules/plannedSchedules-controller";
import { format } from "date-fns";

interface ModalManualAcceptProps {
  onCancel: () => void;
  onConfirm: () => void;
  alreadyAccepted: AlreadyAccepted | null;
  pgName: string; // название ПГ из API /loaded
}

export const ModalManualAccept: FC<ModalManualAcceptProps> = ({ onCancel, onConfirm, alreadyAccepted, pgName }) => {
  const dateText = alreadyAccepted?.dates?.join(", ") ?? "";

  return (
    <Modal
      title={`${pgName} акцептован ${dateText}. Выполнить повторный акцепт?`}
      onCancel={onCancel}
      width={600}
      height={150}
      onConfirm={onConfirm}
      confirmText="Выполнить"
      cancelText="Отменить"
      dataTestConfirmButton="opened-pg-confirm-accept-modal.confirm-button"
    />
  );
};
