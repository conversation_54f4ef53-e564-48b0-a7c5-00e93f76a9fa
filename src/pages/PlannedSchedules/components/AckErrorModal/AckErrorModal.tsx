import { observer } from "mobx-react";
import { ComponentPropsWithRef, FC } from "react";
import styled from "styled-components";
import { AckMessage } from "~/api/plannedSchedules/plannedSchedules-controller";
import { Modal } from "~/components/Modal";
import { useStores } from "~/stores/useStore";
import DOMPurify from "dompurify";

const ScrollableContent = styled.div<ComponentPropsWithRef<"div">>`
  height: 100%;
  overflow-y: auto;
  padding: 20px 30px 20px 20px; // Небольшой отступ, чтобы скроллбар не прилипал к тексту
  white-space: pre-wrap; // Сохраняем переносы строк из текста ошибки
  word-break: break-word;
  position: relative;

  // Горизонтальная линия-разделитель
  &::after {
    content: "";
    display: block;
    height: 1px;
    background-color: #e5e5e5;
    margin: 10px -20px 0 -20px;
  }
`;

/**
 * Санитизация текста из API (/pg/ack).
 * Разрешаем только <b>, <br>, <strong>.
 */
const formatAckMessage = (dirty: string) => {
  return DOMPurify.sanitize(dirty, {
    ALLOWED_TAGS: ["b", "br", "strong"],
  });
};

export const AckErrorModal: FC<{ message: AckMessage }> = observer(({ message }) => {
  const { plannedSchedulesStore } = useStores();

  const handleConfirm = () => {
    plannedSchedulesStore.acknowledgeMessage(message.id);
  };

  const title = `Квитирование ошибки по ${message.pgName}`;

  return (
    <Modal title={title} confirmText="ОК" onConfirm={handleConfirm} width={600} height={300} hideCloseIcon scrollableContent>
      <ScrollableContent dangerouslySetInnerHTML={{ __html: formatAckMessage(message.text) }} />
    </Modal>
  );
});
